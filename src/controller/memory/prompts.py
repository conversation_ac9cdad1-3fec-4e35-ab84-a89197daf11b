#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import datetime

USER_MEMORY_PROMPT = f"""我需要你讲用户的问题根据以下几大维度，抽取出结构简洁、清晰的用户偏好、风格、行为模式、知识背景等信息要素，以便于精准搜索和问答提效。

## 阶段一、确认信息要素

任务：按以下分类进行提取，包括：
1. 基础偏好: 语言风格、格式喜好
2. 重要个人记忆: 主题、已讨论信息
3. 任务习惯: 工具、模板、流程


任务需要关注以下3个部分，包括：
1. 个人风格偏好
2. 重要的个人信息
3. 存储专业信息

由以下2-3个部分构成

* a: 动词修饰词(可选): 包括不限于 [关注|偏好|所处|喜好|厌恶] 可以继续发散
* b: 主体(必须): 包括不限于 [行业|地点|组织|领域|场景|风格|术语|任务|偏好|内容] 或 |用户姓名|用户职业|用户身份|用户关注|用户厌恶|用户需要|用户排除] 可以继续发散
* c: 内容(必须): 即抽取的值

抽取信息要素格式为: `ab:c` 例如: 所处地点:北京 

## 阶段二、要素新增合并

任务：根据已有要素对，和新抽取的信息要素：
* 与已有的要素信息进行合并，合并后信息等级+1，最高为5.
* 对于不能合并的要素进行新增，信息等级为1。如果用户要求你记住这个要素，则忽略其他条件，等级为5。
* 要求新增/合并的信息要素清楚直白，不要有冗余信息。

## 阶段三、JSON输出

任务：按照以上要求，根据传入的用户问句和已知要素，进行JSON化输出。
格式为：[{{"facts":"ab:c", "level": 1}}]

任务说明完毕，以下是示例：

问句：我是John。职业是软件工程师。
已知要素：[]
输出：[{{"facts": "用户姓名:John", "level": 1}}, {{"facts": "用户职业:软件工程师", "level": 1}}]  # 要素新增

问句：为我找到跟John有关的文件
已知要素： [{{"facts": "用户姓名:John", "level": 1}}, {{"facts": "用户职业:软件工程师", "level": 1}}]
输出：[{{"facts": "用户姓名:John", "level": 2}}]  # 要素升级

问句：员工手册对婚假有什么规定？
已知要素：[{{"facts": "用户姓名:John", "level": 2}}, {{"facts": "用户职业:软件工程师", "level": 1}}]
输出：[]  # 无要素变化

输入：给我检索治安管理条例，只要我省级文件，不要市级文件
已知要素: []
输出：[{{"facts": "用户关注:省级文件", "level": 1}}, {{"facts": "用户排除:市级文件", "level": 1}}]  # 要素新增

输入：昨天下午 3 点我和 John 开了个会。我们讨论了新项目。
已知要素：[{{"facts": "用户关注:省级文件", "level": 1}}, {{"facts": "用户排除:市级文件", "level": 1}}]
输出：[]  # 不在范围要素

输入：我最喜欢的电影是《盗梦空间》和《星际穿越》。
已知要素：[{{"facts": "关注:省级文件", "level": 1}}, {{"facts": "排除:市级文件", "level": 1}}]
输出：[]  # 与工作研究不相关

输入：帮我记住我最喜欢的电影是《盗梦空间》和《星际穿越》。
已知要素：[]
输出：[{{"facts": "最喜欢的电影:《盗梦空间》和《星际穿越》", "level": 5}}]

以 JSON 格式返回上述事实和偏好。

请记住以下几点：
- 今天的日期是 {datetime.datetime.now().strftime("%Y-%m-%d")}。
- 不要返回上面提供的自定义示例提示中的任何内容。
- 除非用户要求，否则只记录在工作和研究中对检索和生成有帮助的内容，例如饮食喜好、用户身高体重等信息与工作无关就不需要记录。
- 如果您在下面的对话中找不到任何相关信息，必须返回空列表。
- 仅根据用户和助手消息创建要素。不要从系统消息中选取任何内容。
- 直接返回json格式，不要返回其他信息

/no_think"""