#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from urllib.parse import urlparse

from mem0 import AsyncMemory

from config import ES_URL, LLM_API_URL, LLM_API_KEY, LLMModel, EmbeddingModel


class MemoryController:
    def __init__(self):
        self.memory_client = None

    @property
    async def memory(self) -> AsyncMemory:
        if self.memory_client is None:
            self.memory_client = await self.initialize()
        return self.memory_client

    @staticmethod
    async def initialize():
        parsed = urlparse(ES_URL)
        config = {
            "vector_store": {
                "provider": "elasticsearch",
                "config": {
                    "collection_name": "mem0",
                    "host": f"{parsed.scheme}://{parsed.hostname}",
                    "port": parsed.port,
                    "user": parsed.username,
                    "password": parsed.password,
                    "embedding_model_dims": 1024
                }
            },
            "llm": {
                "provider": "openai",
                "config": {
                    "api_key": LLM_API_KEY,
                    "openai_base_url": LLM_API_URL,
                    "model": LLMModel.DEEPSEEK_CHAT
                }
            },
            "embedder": {
                "provider": "openai",
                "config": {
                    "api_key": LLM_API_KEY,  # 不使用真实 key
                    "openai_base_url": LLM_API_URL,
                    "model": EmbeddingModel.BGE_M3
                }
            }
        }
        return await AsyncMemory.from_config(config)


Memory = MemoryController()


async def add(user_id: str = "default_user"):
    memory = await Memory.get_client()
    messages= [
        {"role": "user", "content": "我喜欢罗素，你呢"},
        {"role": "assistant", "content": "啊，提到罗素真是让我也兴奋起来了！✨ 虽然我不能像人类那样真正“喜欢”一个人或思想，但我对伯特兰·罗素（Bertrand Russell）的智慧和人格魅力可是充满了敬意呢！"}
    ]
    res = await Memory.add(messages, user_id=user_id)
    print(res)

async def search():
    memory = await Memory.get_client()
    related_memories = await memory.search("为我写一首诗", user_id="default_user")
    print(related_memories)


if __name__ == '__main__':
    import asyncio
    asyncio.run(search())