#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import json
import os
import time
import functools
from typing import Optional, Callable, Any

import jieba.analyse

from engine.es import es
from common.logger import logger


BASE_DIR = os.path.dirname(os.path.abspath(__file__))


def get_stopwords():
    stopwords_path = os.path.join(BASE_DIR, "stopwords.txt")
    with open(stopwords_path, mode="r", encoding="utf-8") as file:
        return [line.strip() for line in file.readlines() if line.strip()]


def get_synonyms():
    synonyms_path = os.path.join(BASE_DIR, "synonyms.json")
    with open(synonyms_path, mode="r", encoding="utf-8") as file:
        synonyms = json.load(fp=file)
    return synonyms


def get_vocab():
    vocab_path = os.path.join(BASE_DIR, "vocab.json")
    with open(vocab_path, mode="r", encoding="utf-8") as file:
        vocab = json.load(fp=file)
    return vocab

def async_time_cost(name: Optional[str] = None) -> Callable:
    """装饰器：计算函数执行时间并记录日志

    Args:
        name: 自定义日志名称，如果为None则使用方法名

    Returns:
        装饰器函数
    """

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            # 使用提供的名称或函数名
            log_name = name or func.__name__
            # 记录开始时间
            start_time = time.time()
            # 执行原函数
            result = await func(*args, **kwargs)
            # 计算耗时
            cost_time = time.time() - start_time
            # 记录日志
            if isinstance(retriever := args[0], BaseRetriever):
                logger.info(f"{retriever.log_prefix}{log_name} 耗时 {cost_time:.3f}s")
            else:
                logger.info(f"{log_name} 耗时 {cost_time:.3f}s")
            return result

        return wrapper

    return decorator

class BaseRetriever:
    stopwords = get_stopwords()
    synonyms = get_synonyms()
    vocab = get_vocab()

    def __init__(self):
        self.log_prefix = ""
        self.query: str | None = None

    @staticmethod
    async def es_tokens(query: str, analyzer: str, index: str = "repo_0"):
        ik_smart_response = await es.indices.analyze(
            index=index,
            body={
                "analyzer": analyzer,
                "text": query,
            },
        )
        tokens = [token["token"] for token in ik_smart_response["tokens"]]

        return tokens

    async def get_query_token_weight(self, threshold: float = 0.1):
        if self.query is None:
            raise ValueError("`get_query_token_weight`需要query参数处理完毕")
        token_weight = {}

        smart_tokens = await self.es_tokens(query=self.query, analyzer="ik_smart")

        # 每有一个重复的词,说明此词可能比较重要,权重加0.3
        for token in smart_tokens:
            if token in token_weight:
                token_weight[token] += 0.3
            else:
                token_weight[token] = 1

        # 使用max_word最细粒度分词
        max_word_tokens = await self.es_tokens(query=self.query, analyzer="ik_max_word")

        for token in max_word_tokens:
            # 如果最细粒度分词在停用词中,或者在词库却属于不利于召回的词性,不加入召回
            if token in self.stopwords:
                continue
            if token in self.vocab:
                # c:连词  p:介词  u:助词  o:拟声词  zg:低价值词?
                if self.vocab[token]["s"] in ("c", "p", "u", "o", "zg"):
                    continue

            # 如果max_word分词的词不在smart的分词中长度大于1,且词被某个已有的词完全包含,则权重等于长词
            if token not in token_weight:
                if len(token) > 1:
                    for tw in token_weight.keys():
                        if token in tw:
                            token_weight[token] = token_weight[tw]
                            break
                    else:
                        token_weight[token] = 0.3
                # 否则这个词仅给与比较低的分数
                else:
                    token_weight[token] = 0.3

        # 使用jieba关键词,如果关键词和已提取词有匹配,则对该词的权重乘以平滑处理的关键词权重
        keywords = jieba.analyse.extract_tags(self.query, topK=5, withWeight=True)
        for kw, weight in keywords:
            if kw in token_weight and kw not in self.stopwords:
                token_weight[kw] *= (1 + weight * 0.2)

        # 对一个字的分词，太容易干扰，降低权重为原来的1/3
        for word, weight in list(token_weight.items()):
            if len(word) == 1:
                token_weight[word] = weight / 3

        for word, weight in list(token_weight.items()):
            if word not in self.synonyms:
                continue
            for synonym_word in self.synonyms[word]:
                if synonym_word in self.stopwords:
                    continue
                if synonym_word in token_weight:
                    token_weight[synonym_word] += weight * 0.1
                else:
                    token_weight[synonym_word] = weight * 0.1

        token_weight = {k: round(v, 2) for k, v in token_weight.items() if round(v, 2) >= threshold}
        token_weight = dict(sorted(token_weight.items(), key=lambda item: item[1]))
        logger.info(self.log_prefix + f"问句关键词权重: {json.dumps(token_weight, ensure_ascii=False)}")

        return token_weight
