#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import json
import uuid
import math
from typing import List

from config import RerankerModel, EmbeddingModel
from engine.es import es
from model.doc import get_index
from common.logger import logger
from controller.engine import EmbeddingEngine, RerankEngine, ChatHistory
from controller.retriever.base import BaseRetriever, async_time_cost
from controller.retriever.rewrite import RewriteEngine, LLMModel
from controller.operator.chunking.base import RetrieveChunkModel


class HybridRetrieval(BaseRetriever):
    """混合检索系统，结合BM25和向量检索方法进行文档搜索
    
    该类实现了一个混合检索系统，通过组合BM25关键词匹配和向量相似度计算两种方式
    对文档进行检索和排序，以提高检索的准确性和多样性。
    """
    def __init__(self,
                 request_id: str = None,
                 repo_ids: list[int] = None,
                 doc_ids: list[int] = None,
                 max_doc_size: int = 10,
                 max_doc_chunk_size: int = None,
                 bm25_weight: float = 1,
                 embedding_weight: float = 1,
                 rewrite_engine: RewriteEngine = RewriteEngine(LLMModel.QWEN3_30B),
                 embedding_engine: EmbeddingEngine = EmbeddingEngine(EmbeddingModel.BGE_M3),
                 rerank_engine: RerankEngine = RerankEngine(RerankerModel.BGE_RERANKER_V2_M3),
                 rerank_max_size: int = 30,
                 rerank_threshold: float = 0.2,
                 topn: int = 20):
        """构建混合检索
        todo: 时间序列的权重控制

        Args:
            request_id: 检索请求的唯一标识符，如果为None则自动生成
            repo_ids: 待检索的知识库ID列表
            doc_ids: 待检索的文档ID列表
            max_doc_size: 保留最多多少个文档筛选范围 必须非None
            max_doc_chunk_size: 每个文档保留最多多少片段数, None时自动计算
            bm25_weight: BM25检索结果的权重系数，默认为1
            embedding_weight: 向量检索结果的权重系数，默认为1
            rewrite_engine: 问题改写引擎实例
            embedding_engine: 文本向量化引擎实例
            rerank_engine: 检索结果重排序引擎实例
            rerank_max_size: rerank engine放入的片段数,无rerank_engine则无效
            rerank_threshold: rerank engine的保留阈值,无rerank_engine则无效
            topn: 最终最大片段数量

        """
        super().__init__()
        if bm25_weight < 0 or embedding_weight < 0 or (bm25_weight + embedding_weight) < 0:
            raise ValueError("bm25_weight/embedding_weight必须>=0, 且加和>0")

        self.request_id = request_id or uuid.uuid4().hex
        self.log_prefix = f"混合召回[{self.request_id}] "
        self.repo_ids = repo_ids
        self.doc_ids = doc_ids
        self.max_doc_size = max_doc_size
        self.max_doc_chunk_size = max_doc_chunk_size
        self.bm25_weight = bm25_weight
        self.embedding_weight = embedding_weight
        self.embedding_engine = embedding_engine
        self.rewrite_engine = rewrite_engine
        self.rerank_engine = rerank_engine
        self.rerank_max_size = rerank_max_size
        self.rerank_max_tokens = 512
        self.rerank_batch_size = 16
        self.rerank_threshold = rerank_threshold
        self.topn = topn

        self.index = get_index(repo_ids=repo_ids)  # 索引index范围

        self.query: str | None = None  # 原始问句,retrieve开始时阶段补足
        self.history: list[ChatHistory] | None = None  #  历史问答,retrieve开始时阶段补足
        self.query_rewrite: str | None = None  # 多轮改写问题,如果有rewrite_engine,在rewriting补足
        self.query_keywords: list[str] | None = None  # 问题关键词,如果有rewrite_engine,在rewriting补足
        self.query_associative_keywords: str | None = None  # 问题联想词,如果有rewrite_engine,在rewriting补足
        self.query_search_terms: str | None = None  # 问题扩展,如果有rewrite_engine,在rewriting补足

        self.query_vector: list[float] | None = None  # 问句向量,如果有query_rewrite则使用query_rewrite,否则使用query
        self.query_token_weight: dict | None = None  # 问句关键词及权重,,如果有query_rewrite则使用query_rewrite,否则使用query
        self.chunks: list[dict] | None = None  # 最终chunks格式

        # 默认文档字段权重
        self.field_boost = {
            "filename": 4,
            "source": 4,
            "author": 2,
            "title": 2,
            "plain_text": 1,
        }
        # 默认分片字段权重
        self.chunk_field_boost = {
            "title": 2,
            "plain_content": 1,
        }

        self._es_max_score = 3.4028235e+38

    @async_time_cost()
    async def retrieve(self, query: str, history: list[ChatHistory] = None) -> List[RetrieveChunkModel]:
        self.query = query.strip()
        self.history = history
        logger.info(self.log_prefix + f"开始文档召回: {self.query}")

        await self.rewriting()
        if self.rewrite_engine:
            self.query_token_weight = await self.get_query_token_weight_with_rewrite()
        else:
            self.query_token_weight = await self.get_query_token_weight()

        await self.get_query_vector()
        doc_scores = await self.filter_doc()
        if not doc_scores:
            logger.info(self.log_prefix + f"文档未命中,返回空列表")
            return []

        source_chunks = await self.search(doc_scores=doc_scores)
        source_chunks = self.coarse_ranking(source_chunks=source_chunks)
        source_chunks = await self.model_reranking(source_chunks=source_chunks)
        self.chunks = source_chunks[:self.topn]
        return [RetrieveChunkModel(**chunk) for chunk in self.chunks]

    @async_time_cost()
    async def rewriting(self):
        if self.rewrite_engine is None:
            return
        rewrite_res = await self.rewrite_engine.rewrite(query=self.query, history=self.history)
        logger.info(self.log_prefix+f"问题改写: {json.dumps(rewrite_res, ensure_ascii=False)}")

        self.query_rewrite = rewrite_res.get("query_rewrite")
        self.query_keywords = rewrite_res.get("query_keywords")
        self.query_associative_keywords = rewrite_res.get("query_associative_keywords")
        self.query_search_terms = rewrite_res.get("query_search_terms")

    async def get_query_token_weight_with_rewrite(self, threshold=0.4):
        """
        当有rewrite engine时，原始基于词库、jieba抽取关键词的方法不如扩展问句和关键词，因此需要优化方法

        Returns:

        """
        # 从扩展问句里获取es关键词
        token_weight = {}
        for query in self.query_search_terms:
            smart_tokens = await self.es_tokens(query=query, analyzer="ik_smart")
            # 每有一个重复的词,说明此词可能比较重要,权重加0.3
            for token in smart_tokens:
                if token in token_weight:
                    token_weight[token] += 0.3
                else:
                    token_weight[token] = 1


        # 反而可能是未扩展的改写问题一般会比较弱,但保留一半权重进行兜底
        smart_tokens = await self.es_tokens(query=self.query_rewrite, analyzer="ik_smart")
        for token in smart_tokens:
            if token in token_weight:
                token_weight[token] += 0.15
            else:
                token_weight[token] = 0.3

        # 添加问题关键词
        for keyword in self.query_keywords:
            if keyword in token_weight:
                token_weight[keyword] += 2
            else:
                token_weight[keyword] = 2

        # 添加问题联想词
        for keyword in self.query_associative_keywords:
            if keyword in token_weight:
                token_weight[keyword] += 2
            else:
                token_weight[keyword] = 2

        token_weight = {k: round(v, 2) for k, v in token_weight.items() if v >= threshold}
        token_weight = dict(sorted(token_weight.items(), key=lambda item: item[1]))
        logger.info(self.log_prefix + f"问句关键词权重: {json.dumps(token_weight, ensure_ascii=False)}")

        return token_weight

    @async_time_cost()
    async def get_query_vector(self):
        if self.embedding_weight > 0 and self.embedding_engine is not None:
            vectors = await self.embedding_engine.encode(text=[self.query_rewrite if self.query_rewrite else self.query])
            self.query_vector = vectors[0]

    @async_time_cost()
    async def filter_doc(self) -> dict:
        dsl_query = {
            "bool": {
                "should": [
                    {
                        "function_score": {
                            "query": {
                                "bool": {
                                    "should": [
                                        *self.match_doc_query_tokens(),
                                        *self.match_doc_query_keyword(),
                                        *self.match_doc_query_associative_keyword()
                                    ]
                                }
                            },
                            "functions": [
                                *self.function_score_doc_multiply()
                            ],
                            "boost_mode": "multiply"
                        }
                    }
                ],
                "filter": self.build_doc_filters(),
            },
        }
        # logging.info(f"DSL：{json.dumps(dsl_query, ensure_ascii=False, indent=4)}")
        res = await es.search(
            index=self.index,
            query=dsl_query,
            source_includes=[
                "doc_id",
                "filename",
            ],
            size=self.max_doc_size,
            ignore_unavailable=True
        )
        # logging.warning(json.dumps(res.body, ensure_ascii=False, indent=4))

        doc_scores = {}
        for doc in res["hits"]["hits"]:
            # res = await es.explain(index=index, query=dsl_query, id=doc["_source"]["doc_id"])
            doc_scores[doc["_source"]["doc_id"]] = doc["_score"] if isinstance(doc["_score"], float) else self._es_max_score

        logger.info(self.log_prefix + f"粗筛文档范围: {[doc_scores]}")
        return doc_scores

    async def search(self, doc_scores: dict[int, float]):
        conditions = []
        if self.bm25_weight > 0:
            conditions.extend(self.match_chunk_keyword())
            conditions.extend(self.match_chunk_associative_keyword())
        if self.embedding_weight > 0:
            conditions.extend(self.script_cosine_similarity())

        dsl = {
            "bool": {
                "should": [
                    {
                        "nested": {
                            "path": "chunks",
                            "score_mode": "max",
                            "query": {
                                "bool": {
                                    "should": [
                                        {
                                            "function_score": {
                                                "query": {
                                                    "bool": {
                                                        "should": conditions
                                                    }
                                                },
                                                "functions": [
                                                    *self.function_score_chunk_boost(doc_scores=doc_scores)
                                                    # *self.function_score_chunk_multiply()
                                                ],
                                                "boost_mode": "multiply"
                                            },
                                        }
                                    ],
                                    # "must_not": {"terms": {}}  不需要的类型等chunk内的条件
                                }
                            },
                            "inner_hits": {
                                "_source": [
                                    "chunks.cid",
                                    "chunks.title",
                                    "chunks.html_content",
                                    "chunks.plain_content",
                                    "chunks.xpath",
                                    "chunks.token_counts",
                                ],
                                # 每个文档几个片段
                                "size": self.max_doc_chunk_size if self.max_doc_chunk_size is not None else max(self.topn // len(doc_scores), 20)
                            }
                        }
                    }
                ],
                "filter": [
                    {"terms": {"doc_id": [doc_id for doc_id in doc_scores.keys()]}}
                ],
                "minimum_should_match": 1
            }
        }

        res = await es.search(
            index=self.index,
            query=dsl,
            source_includes=[
                "doc_id",
                "filename",
                "data_time"
            ],
            size=self.max_doc_size,
            ignore_unavailable=True
        )
        source_data = []
        for doc in res["hits"]["hits"]:
            self.post_process_doc_annealing(hits=doc["inner_hits"]["chunks"]["hits"]["hits"])
            for i, chunk in enumerate(doc["inner_hits"]["chunks"]["hits"]["hits"]):
                # print(doc["_score"], window["_score"], window["_source"]["content"][:50].replace("\n", ""))
                if chunk["_score"] == "Infinity":
                    chunk["_score"] = self._es_max_score

                source_data.append(
                    {
                        **doc["_source"],
                        **chunk["_source"],
                        "score": chunk["_score"]
                    }
                )
        source_data = list(sorted(source_data, key=lambda x: x["score"], reverse=True))
        return source_data

    def coarse_ranking(self, source_chunks: list):
        """由于切片会导致片段中可能丢失上下文的核心词,导致可能无关的某个片段因为获得某个词的累积效应而排在较高位置
        为了解决这个问题,在Python层面做后处理,在粗筛后重新进行排序
        注意: 此方法只依靠idf思想,与原始算法并无关系;重算池子中TOPN相关文档的词频出现率对分数进行提权,仍依赖ES分数;

        rerank是不稳定的方法,只对一些极端情况有正向作用,故生效情况需要较为严格.目前对文档进行分数增强需要经过2个条件: 该词出现的较少/包含该词的文档较少
        """
        if not source_chunks:
            return source_chunks

        # 只取解析出来的词中,
        rerank_tokens = {word: weight for word, weight in self.query_token_weight.items() if len(word) > 1 and weight > 0.5}
        query_keyword_count = {word: 0 for word, weight in rerank_tokens.items()}
        doc_contains_count = {word: 0 for word, weight in rerank_tokens.items()}

        for chunk in source_chunks:
            chunk["judge_text"] = ("-".join(chunk["title"]) + chunk["plain_content"] if chunk["title"] else chunk["plain_content"]).replace("\n", "").replace(" ", "")
            # 加和每个结果的关键词数量
            for kw in query_keyword_count:
                query_keyword_count[kw] += chunk["judge_text"].count(kw)
                doc_contains_count[kw] += 1 if kw in chunk["judge_text"] else 0

        # 总平均词频 = 总词频数 / 词数 + 1
        if not query_keyword_count:
            avg_kw_count = 1
        else:
            avg_kw_count = int(sum(list(query_keyword_count.values())) / len(query_keyword_count) + 1)
        # 为低词频文档加权
        for word, count in query_keyword_count.items():
            if count == 0:
                continue

            # 单个词权重倍率 = 总平均词频 / 无变动倍率 / 单词词频
            weight = avg_kw_count / 1.5 / count
            # 无变动倍率保证单词词频略微超过总平均词频时,不会产生分数变动
            # 包含该词的文档占比 / 召回文档数 >= 0.25时,不会产生分数变动
            if weight <= 1 or doc_contains_count[word] / len(source_chunks) >= 0.25:
                continue

            # 分数放大倍率
            low_freq_max_weight = 3  # 单词最大分数放大率
            ratio = min(1 + math.log(weight), low_freq_max_weight)  # [1, low_freq_max_weight]
            for chunk in source_chunks:
                if word in chunk["judge_text"]:
                    chunk["score"] *= ratio

        for chunk in source_chunks:
            if "judge_text" in chunk:
                del chunk["judge_text"]

        # 返回粗排后topn1.5倍数量的片段
        return list(sorted(source_chunks, key=lambda x: x["score"], reverse=True))[:self.rerank_max_size]

    @async_time_cost()
    async def model_reranking(self, source_chunks: list[dict]):
        if not self.rerank_engine:
            return source_chunks
        if len(source_chunks) == 1:
            return source_chunks

        rerank_text_mapping = {}
        cid_scores = {}
        for chunk in source_chunks:
            filename_title_prefix = f"{chunk['filename']}-{'_'.join(chunk['title'])}:"
            prefix_token_counts = len(filename_title_prefix)
            cid_scores[chunk["cid"]] = []
            if prefix_token_counts + chunk["token_counts"] <= self.rerank_max_tokens:
                rerank_text_mapping[filename_title_prefix+chunk["plain_content"]] = chunk["cid"]
            else:
                for step in range(0, len(chunk["plain_content"]), self.rerank_max_tokens - prefix_token_counts):
                    rerank_text_mapping[filename_title_prefix + chunk["plain_content"][step:step+self.rerank_max_tokens-prefix_token_counts]] = chunk["cid"]

        rerank_texts = list(rerank_text_mapping.keys())
        for step in range(0, len(rerank_texts), self.rerank_batch_size):
            batch_texts = rerank_texts[step:step+self.rerank_batch_size]
            res = await self.rerank_engine.rerank(query=self.query, docs=batch_texts)
            for r in res:
                cid = rerank_text_mapping[batch_texts[r["index"]]]
                cid_scores[cid].append(r["relevance_score"])
        for source_chunk in source_chunks:
            source_chunk["relevance_score"] = max(cid_scores[source_chunk["cid"]]) if cid_scores[source_chunk["cid"]] else 0

        source_chunks = list(sorted(source_chunks, key=lambda x: x["relevance_score"], reverse=True))[:self.topn]
        if self.rerank_threshold:
            source_chunks = [chunk for chunk in source_chunks if chunk["relevance_score"] >= self.rerank_threshold]
        return source_chunks

    def build_doc_filters(self):
        filters = []
        if self.repo_ids is not None:
            filters.append({"terms": {"repo_id": self.repo_ids}})
        if self.doc_ids is not None:
            filters.append({"terms": {"doc_id": self.doc_ids}})
        return filters

    def match_doc_query_tokens(self):
        """
        [文档筛选策略]根据问句、关键词、联想词等切分出的词和权重,以bm25匹配文档的策略
        Args:

        Returns:

        """
        if len(self.query_token_weight) >= 3:
            keyword_condition = [{
                "term": {
                    field: {
                        "value": t,
                        "boost": round(b * ratio, 1)
                    }
                }
            } for t, b in self.query_token_weight.items() for field, ratio in self.field_boost.items()]
        else:
            keyword_condition = [{
                "multi_match": {
                    "query": self.query_rewrite or self.query,
                    "fields": [f"{field}^{ratio}" for field, ratio in self.field_boost.items()],
                    "type": "best_fields",
                    "tie_breaker": 0.7
                }
            }]
        return keyword_condition

    def match_doc_query_keyword(self):
        """
        [文档筛选策略]根据大模型推理的问句关键词,BM25匹配最相关文档

        Returns:

        """
        if self.query_keywords is None:
            return []

        keyword_condition = [
            *[
                {
                    "multi_match": {
                        "query": kw,
                        "fields": [f"{field}^{ratio}" for field, ratio in self.field_boost.items()],
                        "type": "best_fields",
                        "tie_breaker": 0.7
                    }
                } for kw in self.query_keywords
            ],
            *[
                {
                    "multi_match": {
                        "query": kw,
                        "fields": [f"{field}^{ratio}" for field, ratio in self.field_boost.items()],
                        # "type": "phrase",
                        "analyzer": "ik_max_word",
                        "tie_breaker": 1.0
                    }
                } for kw in self.query_keywords
            ],
        ]

        return keyword_condition

    def match_doc_query_associative_keyword(self):
        """
        [文档筛选策略]根据大模型推理的问题联想词,BM25匹配最相关文档

        Returns:

        """
        if self.query_associative_keywords is None:
            return []

        associative_keyword_condition = [
            *[
                {
                    "multi_match": {
                        "query": kw,
                        "fields": [f"{field}^{ratio}" for field, ratio in self.field_boost.items()],
                        "type": "best_fields",
                        "tie_breaker": 0.7
                    }
                } for kw in self.query_associative_keywords
            ],
            *[
                {
                    "multi_match": {
                        "query": kw,
                        "fields": [f"{field}^{ratio}" for field, ratio in self.field_boost.items()],
                        # "type": "phrase",
                        "analyzer": "ik_max_word",
                        "tie_breaker": 1.0
                    }
                } for kw in self.query_associative_keywords
            ],
        ]
        return associative_keyword_condition

    def function_score_doc_multiply(self, power: float = 1.8):
        """
        [文档筛选策略]根据问句切分出的词和权重,对权重>0.5的词，在命中时累加权重，以实现更多的词命中，而非单一词命中足够多的目的
        Args:
            power:

        Returns:

        """
        multiply_tokens = [word for word, weight in self.query_token_weight.items() if weight > 0.5]

        return [
            {
                "filter": {
                    "term": {f"plain_text": tk}
                },
                "weight": power
            } for tk in multiply_tokens
        ]

    def match_chunk_keyword(self):
        """
        [分片筛选策略] 根据问句、关键词、联想词等切分出的词和权重,以bm25匹配文档的策略
        Returns:

        """
        if len(self.query_token_weight) >= 3:
            keyword_condition = [{
                "term": {
                    field: {
                        "value": t,
                        "boost": round(b * ratio * self.bm25_weight, 2)
                    }
                }
            } for t, b in self.query_token_weight.items() for field, ratio in self.chunk_field_boost.items()]
        else:
            keyword_condition = [{
                "multi_match": {
                    "query": self.query,
                    "fields": [f"{chunk_field}^{ratio}" for chunk_field, ratio in self.chunk_field_boost.items()],
                    "type": "best_fields",
                    "tie_breaker": 0.7,
                    "boost": self.bm25_weight
                }
            }]

        return keyword_condition

    def match_chunk_query_keyword(self):
        """
        [分片筛选策略] 根据大模型提取的关键词,以bm25匹配段落的策略

        Returns:

        """
        if self.query_keywords is None:
            return []

        keyword_condition = [
            *[
                {
                    "multi_match": {
                        "query": kw,
                        "fields": [f"{field}^{ratio}" for field, ratio in self.chunk_field_boost.items()],
                        "type": "best_fields",
                        "tie_breaker": 0.7
                    }
                } for kw in self.query_keywords
            ],
            *[
                {
                    "multi_match": {
                        "query": kw,
                        "fields": [f"{field}^{ratio}" for field, ratio in self.chunk_field_boost.items()],
                        "type": "phrase",
                        "analyzer": "ik_max_word",
                        "tie_breaker": 1.0
                    }
                } for kw in self.query_keywords
            ]
        ]

        return keyword_condition

    def match_chunk_associative_keyword(self):
        """
        [分片筛选策略] 根据大模型提取的联想词，以bm25匹配段落的策略
        Returns:

        """
        if self.query_associative_keywords is None:
            return []

        associated_condition = [
            *[
                {
                    "multi_match": {
                        "query": kw,
                        "type": "best_fields",
                        "fields": [f"{chunk_field}^{ratio}" for chunk_field, ratio in self.chunk_field_boost.items()],
                        "boost": self.bm25_weight
                    }
                } for kw in self.query_associative_keywords
            ],
            *[
                {
                    "multi_match": {
                        "query": kw,
                        "type": "phrase",
                        "fields": [f"{chunk_field}^{ratio}" for chunk_field, ratio in self.chunk_field_boost.items()],
                        "analyzer": "ik_max_word",
                        "boost": self.bm25_weight
                    }
                } for kw in self.query_associative_keywords
            ],
        ]
        return associated_condition

    def script_cosine_similarity(self):
        """
        [分片筛选策略] Embedding策略,受embedding_weight影响权重
        Returns:

        """
        return [{
            "script_score": {
                "query": {
                    "match_all": {}
                },
                "script": {
                    "source": "cosineSimilarity(params.query_vector, 'chunks.vector')",
                    "params": {
                        "query_vector": self.query_vector
                    }
                },
                "boost": round(6 * self.embedding_weight, 2)
            }
        }]

    def function_score_chunk_boost(self, doc_scores: dict, _min: (int, float) = 1, _max: (int, float) = 1.2):
        """压缩分数到[_min, _max]"""
        if not doc_scores:
            return []
        max_value = max(doc_scores.values())
        min_value = min(doc_scores.values())

        normalized_doc_scores = {
            doc_id: round(1 + (score - min_value) * (_max - _min) / (max_value - min_value), 3)
            if max_value > min_value
            else 1
            for doc_id, score in doc_scores.items()
        }
        logger.info(self.log_prefix + f"normalized_doc_scores: {normalized_doc_scores}")
        function_score_normalized = [
            {
                "filter": {
                    "prefix": {f"chunks.cid": f"{k}_"}
                },
                "weight": v
            } for k, v in normalized_doc_scores.items()
        ]
        return function_score_normalized

    def function_score_chunk_multiply(self) -> list[dict] | None:

        multiply_tokens = [word for word, weight in self.query_token_weight.items() if weight > 0.2]
        power = round(math.pow(self._es_max_score, 1 / len(self.query_token_weight)), 2)
        if power > 1.5:
            power = 1.5
        if power < 1.1:
            power = 1.1

        return [
            {
                "filter": {
                    "term": {"chunks.plaintext": tk}
                },
                "weight": power
            } for tk in multiply_tokens
        ]

    @staticmethod
    def post_process_doc_annealing(hits: list):
        """对同一个文档的多个区间进行退火"""
        doc_score_annealing_factor = 0.96

        for i, sr in enumerate(hits):
            sr["_score"] = round(sr["_score"] * doc_score_annealing_factor ** i, 4)


if __name__ == '__main__':
    import asyncio
    retrieval = HybridRetrieval(repo_ids=[6])
    chunks = asyncio.run(retrieval.retrieve(query="介绍一下银河系创投"))
    print(chunks)