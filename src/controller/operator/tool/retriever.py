from typing import Optional, List, Annotated

from pydantic import BaseModel, Field

from controller.operator.chunking import RetrieveChunkModel
from controller.retriever import HybridRetrieval


class RetrieverSearchResult(BaseModel):
    title: Annotated[str, Field(title="标题", description="搜索结果的标题")]
    plain_content: Annotated[Optional[str], Field(title="原始内容", description="搜索结果的原始内容")]
    data_time: Annotated[str, Field(title="数据时间", description="对于上传数据可能无意义")]


async def retriever_search(query: str, max_results: Optional[int] = 5) -> List[RetrieverSearchResult]:
    """
    在知识库中搜索相关信息，检索与查询内容最匹配的文档片段。

    该工具可以帮助你获取系统知识库中的专业数据、历史资料、内部文档和结构化信息。
    适合需要查询内部资料、专业领域知识或历史数据分析的场景。

    Args:
        query: 搜索查询内容，应该是明确、具体的问题或关键词。例如"宁德时代财务状况"、"某公司投资风险分析"等
        max_results: 返回的最大结果数量，默认为5条。增加数量可获取更全面的信息覆盖，但可能包含相关性较低的结果

    Returns:
        List[RetrieverSearchResult]: 搜索结果列表，每个结果包含标题、原始内容文本和数据时间
    """
    retrieval = HybridRetrieval(max_doc_size=max_results)
    results: List[RetrieveChunkModel] = await retrieval.retrieve(query=query)

    return [
        RetrieverSearchResult(
            title=result.title or "无标题",
            plain_content=result.plain_content or "无内容",
            data_time=result.data_time or "未知时间"
        )
        for result in results
    ]


if __name__ == "__main__":
    import asyncio
    result = asyncio.run(retriever_search(query="宁德时代"))
    for i in result:
        print(i.model_dump_json(indent=4))
