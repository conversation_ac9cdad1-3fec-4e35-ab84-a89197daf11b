from openai import AsyncStream
from openai.types.chat import Chat<PERSON><PERSON>pletionChunk
from typing import List

from config import LLMModel
from controller.engine import ChatEngine
from controller.operator.chunking import RetrieveChunkModel
from controller.operator.prompt.doc_qa import doc_qa_prompt
from controller.operator.runner.base import BaseRunner, TokenConsumption
from controller.operator.tool.time import get_current_time
from model.session import ChatHistory


class DocQA(BaseRunner):
    def __init__(self, chunks: List[RetrieveChunkModel], user: str, history: List[ChatHistory] = None,
                 model_name: LLMModel = LLMModel.DEEPSEEK_CHAT, token_consumption: TokenConsumption | None = None):
        super().__init__()

        self.chunks = chunks
        self.user = user
        self.history = history
        self.token_consumption = token_consumption or TokenConsumption()
        self.model_engine = ChatEngine(model_name=model_name, token_consumption=self.token_consumption)

        self.system_prompt = doc_qa_prompt.format(
            current_time=get_current_time(),
            search_results=[chunk.model_dump(include={'cid', 'filename', 'html_content', 'data_time'}) for chunk in self.chunks],
        )

    async def _run(self) -> AsyncStream[ChatCompletionChunk]:
        response = await self.model_engine.generator(
            prompt=self.user,
            system_prompt=self.system_prompt,
            history=self.history,
            stream=True
        )
        return response
