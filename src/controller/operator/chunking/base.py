from abc import abstractmethod
from typing import Annotated, List, Optional

from pydantic import BaseModel, Field, AliasChoices


class ExtractModel(BaseModel):
    subject: Annotated[List[str], Field(title="主体")] = []
    industry: Annotated[List[str], Field(title="行业")] = []
    positive_view: Annotated[List[str], Field(title="正面观点")] = []
    negative_view: Annotated[List[str], Field(title="负面观点")] = []
    sentiment_score: Annotated[Optional[float], Field(title="情绪因子")] = None
    keywords: Annotated[List[str], Field(title="关键信息")] = []
    abstract: Annotated[str, Field(title="摘要")] = None
    analysis: Annotated[Optional[str], Field(title="主观分析")] = None

class ChunkModel(BaseModel):
    index: Annotated[Optional[int], Field(title="索引")] = None
    chunk_text: Annotated[str, Field(title="分块文本")]
    start_offset: Annotated[int, Field(title="起始偏移量")]
    end_offset: Annotated[int, Field(title="结束偏移量")]
    extract_result: Annotated[Optional[ExtractModel], Field(title="提取结果")] = None


class RetrieveChunkModel(BaseModel):
    """retrieve chunk 结构,逐步替换ChunkModel"""
    cid: Annotated[Optional[str], Field(title="分片唯一ID")]
    filename: Annotated[Optional[str], Field(title="文件名")]
    data_time: Annotated[str, Field(title="数据时间", description="对于上传数据可能无意义")] = None
    doc_id: Annotated[int, Field(title="文档ID")]
    xpath: Annotated[List[str], Field(title="xpath表达式")]
    html_content: Annotated[str, Field(title="html格式文本内容")] = None
    token_counts: Annotated[int, Field(title="token数量(默认BGE-M3)")]

    # 非必传参数,默认不输出
    title: Annotated[List[str], Field(exclude=True, title="分片标题")] = None
    plain_content: Annotated[str, Field(exclude=True, title="纯文本内容")] = None


class DocModel(BaseModel):
    title: Annotated[Optional[str], Field(title="标题")] = None
    content: Annotated[str, Field(title="完整文本")]
    data_time: Annotated[Optional[str], Field(title="发布时间")] = None
    chunk_children: Annotated[List[ChunkModel], Field(title="分块文本列表")] = []
    extract_result: Annotated[Optional[ExtractModel], Field(title="提取结果汇总")] = None


class CombineModel(BaseModel):
    layer: Annotated[int, Field(title="层级")] = 1
    chunk_node: Annotated[ChunkModel, Field(title="分块文本模型")]
    children_node: Annotated[Optional[List["CombineModel"]], Field(title="子节点")] = []


class BaseChunking:
    @abstractmethod
    def chunk(self, *args, **kwargs) -> DocModel:
        raise NotImplementedError
