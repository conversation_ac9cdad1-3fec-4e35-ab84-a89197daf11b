import traceback
from datetime import datetime
from enum import StrEnum
from typing import List, Annotated, Optional, AsyncGenerator, AsyncIterable
from loguru import logger as logging

from openai import AsyncStream
from openai.types.chat import ChatCompletionChunk
from pydantic import Field, BaseModel
from mem0 import AsyncMemory

from common.time import now_tz_datestring_with_millis
from config import MAX_HISTORY_LEN, LLMModel
from controller.chat.buffer import QABuffer, BufferType
from controller.chat.session import Session
from controller.operator.chunking import RetrieveChunkModel
from controller.operator.runner.qa.doc_qa import DocQA
from model.session import ChatHistory


class StreamStage(StrEnum):
    THINKING = "thinking"
    GENERATING = "generating"
    CORNER = "corner"
    REFERENCE = "reference"
    ERROR = "error"


class CompletionStreamResponse(BaseModel):
    stage: Annotated[StreamStage, Field(title="思考阶段")]
    session_id: Annotated[str, Field(title="会话ID")]
    request_id: Annotated[str, Field(title="请求ID")]
    create_time: Annotated[datetime, Field(title="创建时间", default_factory=datetime.now)] = None
    content: Annotated[Optional[str], Field(title="回复内容")] = None
    reference: Annotated[Optional[list], Field(title="引用文献")] = None
    error_msg: Annotated[Optional[str], Field(title="错误信息")] = None


class DocQAHelper:
    def __init__(self,
                 session_id: str,
                 chunks: List[RetrieveChunkModel],
                 user: str,
                 user_id: int,
                 history: List[ChatHistory] = None,
                 memory: AsyncMemory = None):
        """
        Parameters:
            chunks: 文档列表[目前只支持范围为DocModel，后续会建立分析器结果与DocModel之间的联系]
            user: 用户输入的问题
        """
        self.session_id = session_id
        self.chunks = chunks
        self.user_prompt = user
        self.user_id = user_id
        self.history = history if history else []
        self.chat_message = ChatHistory(
            user=user,
            query_time=now_tz_datestring_with_millis()
        )
        self.memory = memory

    async def generator(self) -> AsyncGenerator:
        try:
            self.history = self.history or await self.get_chat_history(session_id=self.session_id)

            # 参考文献 ID 的映射是为了避免复杂的 ID 干扰大模型的效果
            chunk_relation = {}
            for index, chunk in enumerate(self.chunks):
                chunk_relation[str(index)] = chunk.cid
                chunk.cid = str(index)

            runner = DocQA(chunks=self.chunks, user=self.user_prompt, history=self.history, model_name=LLMModel.DEEPSEEK_REASONER)
            stream: AsyncStream[ChatCompletionChunk] = await runner.run()

            thinking, assistant = "", ""
            use_cid = set()
            buffer = QABuffer(stream=self._generating(stream=stream), reference=self.chunks)
            async for buff in buffer.generator():
                if buff.type_ == BufferType.THINK_CONTENT:
                    thinking += buff.content
                    yield self.response_chunk(content=buff.content, stage=StreamStage.THINKING)
                elif buff.type_ == BufferType.CONTENT:
                    assistant += buff.content
                    yield self.response_chunk(content=buff.content, stage=StreamStage.GENERATING)
                elif buff.type_ == BufferType.CORNER:
                    assistant += f"[citation:{chunk_relation[buff.content]}]"
                    use_cid.add(buff.content)
                    yield self.response_chunk(content=chunk_relation[buff.content], stage=StreamStage.CORNER)

            use_chunk = []
            for chunk in self.chunks:
                if chunk.cid in use_cid:
                    chunk.cid = chunk_relation[chunk.cid]
                    use_chunk.append(chunk)

            yield self.response_chunk(reference=use_chunk, stage=StreamStage.REFERENCE)

            self.chat_message.reference = use_chunk
            self.chat_message.assistant = assistant
            if thinking:
                self.chat_message.thinking = thinking
            if self.memory:
                await self.memory.add(messages=self.history, user_id=self.session_id)

        except Exception as e:
            self.chat_message.error_msg = ''.join(traceback.format_exception(type(e), value=e, tb=e.__traceback__))
            logging.error(f"Failed to generate completion stream: {self.chat_message.error_msg}")
            yield self.response_chunk(error_msg=self.chat_message.error_msg, stage=StreamStage.ERROR)

        finally:
            logging.info(f"Chat history: {self.chat_message.model_dump_json(indent=4)}")
            await Session.append_to_chat_history(self.session_id, self.chat_message, refresh=True)

        yield "data: [DONE]\n\n"

    @staticmethod
    async def _generating(stream: AsyncIterable):
        thinking, assistant = "", ""
        async for message in stream:
            # 当前仅支持公共版 DeepSeek 的返回格式
            delta = message.choices[0].delta if message.choices and message.choices[0].delta else None

            if reasoning_content := getattr(delta, "reasoning_content", "") if delta else "":
                thinking += reasoning_content
                yield reasoning_content, StreamStage.THINKING

            if content := getattr(delta, "content", "") if delta else "":
                assistant += content
                # 处理开头的换行符，避免连续的换行符导致空内容
                if assistant.startswith("\n"):
                    assistant = assistant.replace("\n", "", 1)
                    continue
                # 为了便于后续处理，每次进入 buffer 的都只有 1 个字符
                for string in content:
                    yield string, StreamStage.GENERATING

    @staticmethod
    async def get_chat_history(session_id: str, max_len: int = MAX_HISTORY_LEN) -> List[ChatHistory]:
        session = await Session.get_one(session_id)
        return session.chat_history[-max_len:]

    def response_chunk(self, error_msg: str = None, content: str = None, reference: list[RetrieveChunkModel] = None,
                       stage: StreamStage = StreamStage.GENERATING) -> str:
        chunk = CompletionStreamResponse(
            stage=stage,
            error_msg=error_msg,
            session_id=self.session_id,
            request_id=self.chat_message.request_id,
            create_time=now_tz_datestring_with_millis(),
            content=content,
            reference=[r.model_dump() for r in reference] if reference else None,
        )
        return f"data: {chunk.model_dump_json()}\n\n"
