#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import re

from lxml import etree, html as lxmlhtml
from transformers import AutoTokenizer

from common.logger import logger
from controller.parser.chunker import Chun<PERSON>, <PERSON>k, ChunkNode
from langchain_text_splitters import RecursiveCharacterTextSplitter


class HtmlChunker(Chunker):
    def __init__(self,
                 tokenizer: AutoTokenizer,
                 max_tokens: int = 1024,
                 max_tolerance_tokens: int = 2048):
        super().__init__(max_tokens=max_tokens, max_tolerance_tokens=max_tolerance_tokens, tokenizer=tokenizer)

        self._last_node: ChunkNode | None = None
        self.replace_one_white_pattern = re.compile(r"(?<!\s)\s(?!\s)")

    def chunk(self, html_content: str):
        tree = etree.fromstring(html_content, lxmlhtml.HTMLParser())
        root = tree.getroottree()  # 用于寻找xpath

        body_tag = tree.xpath('//body')[0]
        context = etree.iterwalk(body_tag, events=("start",))

        for action, element in context:
            tag = element.tag.lower()
            element_text = element.text_content()

            # 过滤空文本节点
            if not element_text.strip():
                continue

            # 无视内部子节点,直接可从以下节点提取所有文本信息的
            # 每个节点提取
            if tag in ("table", "p", "span", "li", "strong", "h1", "h2", "h3", "h4", "h5", "h6"):
                fragments = [
                    self.replace_one_white_pattern.sub("", text.strip())
                    for text in element.xpath('.//text()[normalize-space() != ""]')
                ]
                plain_content = " ".join(fragments).replace("\n", "")

                # 替换element中的"None"为空格
                html_content = lxmlhtml.tostring(element, encoding="unicode", pretty_print=False)
                xpath = root.getpath(element)
                token_counts = len(self.tokenizer.encode(text=html_content))
                if token_counts <= self.max_tolerance_tokens:
                    self.add_node(
                        tag=tag, xpath=xpath, html_content=html_content, plain_content=plain_content,
                        token_counts=token_counts)
                else:
                    logger.warning(
                        f"标签[{tag}] tokens [{token_counts}] 已超过最大容忍值 [{self.max_tolerance_tokens}] 按照标签进行细粒度处理")
                    # 当table的tokens超过长度时，跳过并记录th列头，并将每一行的正文的html和列头拼凑
                    if tag == "table":
                        # 对于超长表格,单独按行处理
                        self.chunk_large_table_tag(element=element, root=root)
                    else:
                        # 对于其他超长标签，使用文本切分方法
                        self.chunk_large_text_tag(element=element, root=root)
                # 子节点全部从内存中删除
                for child in element:
                    element.remove(child)
                continue

            if len(element) == 0:
                # todo: 完全测试后删除
                logger.error(f"有未命中的节点"
                             f"Tag: {tag}, "
                             f"Text: {element.text_content()} "
                             f"Xpath: {root.getpath(element)} "
                             f'HTML: {lxmlhtml.tostring(element, encoding="unicode", pretty_print=True)} ')

        return self.chunks

    def add_node(self, tag: str, xpath: str, html_content: str, plain_content: str, token_counts: int,
                 origin_plain_content: str = None):
        """
        将节点添加到当前块或如果节点可以分割则创建新块。该方法对纯文本内容进行编码以计算token数量，
        判断块是否可分割，并适当地将节点添加到最后一个块或新创建的块中。

        参数:
            tag (str): 要添加的节点的标签名称。
            xpath (str): 文档中节点的XPath位置。
            html_content (str): 节点内容的HTML表示。
            plain_content (str): 节点内容的纯文本表示。
            token_counts (int): 节点内容的token数量。
            origin_plain_content (str): 原始文本,仅用来做记录和拼接正文.不存在时取plain_content
        """
        node = ChunkNode(
            tag=tag, xpath=xpath, plain_content=plain_content, html_content=html_content, token_counts=token_counts,
            origin_plain_content=origin_plain_content
        )

        if self._is_chunk_splittable(node=node):
            self.chunks.append(Chunk())

        if self._last_node is None:
            self.chunks.append(Chunk())
        self._last_chunk.add_node(node)

        self._last_node = node

    def chunk_large_table_tag(self, element: etree.Element, root: etree.ElementTree):
        """
        按行切换大型表格
        todo: 这里还应该需要考虑合并单元格问题
        Args:
            element:
            root:

        Returns:

        """
        # 提取表头信息
        header_elements = element.xpath('.//tr[th]')
        header_html = ""
        header_plain_text = ""

        if header_elements:
            # 获取第一个包含th的tr作为表头
            header_row = header_elements[0]
            header_html = lxmlhtml.tostring(header_row, encoding="unicode", pretty_print=False)
            header_plain_text = self._extract_plain_text(element=header_elements)

        # 处理每一行数据
        data_rows = element.xpath(".//tr[td]")  # 获取包含td的行

        for i, row in enumerate(data_rows):
            row_html = lxmlhtml.tostring(row, encoding="unicode", pretty_print=False)

            # 将列头和当前行组合成完整的table
            if header_html:
                combined_html = f"<table><tbody>{header_html}{row_html}</tbody></table>"
            else:
                combined_html = f"<table><tbody>{row_html}</tbody></table>"

            extract_origin_text = self._extract_plain_text(element=row)
            row_origin_content = extract_origin_text + "\n"  # 对于大表格行,每行数据后纯文本紧跟1换行
            # 第一行纯文本携带列头
            if i == 0:
                row_origin_content = header_plain_text + "\n" + row_origin_content
            row_plain_content = header_plain_text + " " + extract_origin_text

            # 计算组合后的token数量
            combined_token_counts = len(self.tokenizer.encode(text=combined_html))
            row_xpath = root.getpath(row)

            self.add_node(
                tag=f"table_part",
                xpath=row_xpath,
                html_content=combined_html,
                plain_content=row_plain_content,
                origin_plain_content=row_origin_content,
                token_counts=combined_token_counts
            )

    def chunk_large_text_tag(self, element: etree.Element, root: etree.ElementTree):
        """
        对于超长文本标签：先提取出标签下的纯文本，进行纯文本切分，然后将切分后的纯文本重新包装为html标签

        Args:
            element:
            root:

        Returns:

        """
        # 提取元素下的纯文本
        plain_text = self._extract_plain_text(element)

        # 使用文本分割器进行切分
        text_splitter = RecursiveCharacterTextSplitter(
            separators=["\n\n", "\n", "。", "！", "!", "？", "?", "，", " "],
            chunk_size=self.max_tokens,
            chunk_overlap=0
        )

        # 分割文本
        text_chunks = text_splitter.split_text(plain_text)
        # 获取原始标签名和xpath
        tag = element.tag.lower()
        xpath = root.getpath(element)

        # 为每个文本块创建HTML标签并添加节点
        for chunk_text in text_chunks:
            # 重新包装为HTML标签
            html_content = f"<{tag}>{chunk_text}</{tag}>"

            # 计算token数量
            token_counts = len(self.tokenizer.encode(text=html_content))

            # 添加节点
            self.add_node(
                tag=f"{tag}_part",
                xpath=xpath,
                html_content=html_content,
                plain_content=chunk_text,
                token_counts=token_counts
            )


    def _is_chunk_splittable(self, node: ChunkNode) -> bool:
        if not self.chunks:
            return False

        # token长度超过设定值
        if self._last_chunk.token_counts + node.token_counts > self.max_tokens:
            return True

        # 上一个节点非标题,但此节点为标题,进行切分
        if self._last_node and self._last_node.title_type is None and node.title_type:
            return True

        # 此节点为大节点拆分的行
        if node.tag.endswith("part"):
            return True

        return False

    def _extract_plain_text(self, element: etree.Element):
        element_fragments = []
        elements = [element] if not isinstance(element, list) else element
        for e in elements:
            for text in e.xpath('.//text()[normalize-space() != ""]'):
                text = self.replace_one_white_pattern.sub("", text.strip())
                element_fragments.append(text)

        return " ".join(element_fragments).replace("\n", "")

    @property
    def _last_chunk(self) -> Chunk | None:
        if not self.chunks:
            return None
        return self.chunks[-1]
