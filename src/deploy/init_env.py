from urllib.parse import quote_plus

import sqlalchemy
from elasticsearch import Elasticsearch
from sqlalchemy import create_engine

from config import ES_URL, MYSQL_DB, MYSQL_CHARSET, MYSQL_HOST, MYSQL_PASSWORD, MYSQL_PORT, MYSQL_USER
from model.session import CHAT_SESSION_INDEX, CHAT_SESSION_MAPPING
from model.doc import get_index, DOC_MAPPING
from model.analytics_metrics_es import METRICS_INDEX, METRICS_MAPPING
from model.summarizer_task_es import SUMMARIZER_TASK_INDEX, SUMMARIZER_TASK_MAPPING
from model.base import BaseModel
from controller.repository import Repo
from controller.admin.user import User
from controller.config import Strategy, ModelInfo


class DeployInit:
    def __init__(self):
        self.init_engine = None

    def create_database(self):
        """
        创建知识库
        在docker-compose中每次都会执行，以方便初始化
        如知识库已经存在，则会提示知识库已存在，不影响后续流程
        """
        connection_url = f"mysql+pymysql://{MYSQL_USER}:{quote_plus(MYSQL_PASSWORD)}@{MYSQL_HOST}:{MYSQL_PORT}?charset={MYSQL_CHARSET}"
        create_db_engine = create_engine(url=connection_url, echo=True)
        with create_db_engine.connect() as conn:
            conn.execute(sqlalchemy.text("commit"))
            conn.execute(sqlalchemy.text("CREATE DATABASE IF NOT EXISTS {db}".format(db=MYSQL_DB)))
        connection_url = f"mysql+pymysql://{MYSQL_USER}:{quote_plus(MYSQL_PASSWORD)}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DB}?charset={MYSQL_CHARSET}"
        self.init_engine = create_engine(
            url=connection_url,
            pool_recycle=300,
            pool_size=20,
            max_overflow=15,
            pool_timeout=15,
            echo=False,
        )

    @staticmethod
    def create_tables():
        """
        创建表
        注意: 被引用的表文件才会被创建
        如数据表已存在，则会跳过表的创建，不影响后续流程
        """
        from model import (repository, summarizer, auth, analytics, token_counts, strategy, model_info)
        from engine.rdb import engine_sync
        BaseModel.metadata.create_all(engine_sync)

    @staticmethod
    def init_index():
        """初始化ES index,记录模型服务相关的请求日志,并支持服务记录和历史等功能"""
        es = Elasticsearch(ES_URL, request_timeout=60, retry_on_timeout=True)

        for index, mapping in (
            (get_index([0]), DOC_MAPPING),
            (CHAT_SESSION_INDEX, CHAT_SESSION_MAPPING),
            (METRICS_INDEX, METRICS_MAPPING),
            (SUMMARIZER_TASK_INDEX, SUMMARIZER_TASK_MAPPING)
        ):
            if es.indices.exists(index=index):
                print(f"ES index '{index}' is exists. skip create mapping")
                continue
            try:
                res = es.indices.create(index=index, body=mapping)
            except Exception as err:
                print(f"ES index '{index}' create failed.reason: {str(err)}")
            else:
                if res["acknowledged"] is True:
                    print(f"ES index '{index}' create success")
                else:
                    print(f"ES index '{index}' create failed.reason: {str(res)}")

    @staticmethod
    def init_data():
        for func in (
            Repo.init_sync,
            User.init_sync,
            Strategy.init_sync,
            ModelInfo.init_sync
        ):
            try:
                func()
            except sqlalchemy.exc.IntegrityError:
                pass


if __name__ == "__main__":
    deploy_init = DeployInit()
    deploy_init.create_database()
    deploy_init.create_tables()
    deploy_init.init_index()
    deploy_init.init_data()
