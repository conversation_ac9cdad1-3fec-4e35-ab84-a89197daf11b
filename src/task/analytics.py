#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import asyncio
import copy
import datetime
from itertools import chain

from common.logger import logger
from engine.rdb import g
from controller.analytics import (Dashboard, Metrics, MetricType, MetricResult, AnalyticsTaskStatus, Summarizer,
                                  SummarizerTask)
from controller.operator.workflow.analyze import (AnalyzeWorkflow, AbstractResultModel, QuantifyIndicatorModel,
                                                IndicatorType, NumberConfig, StringConfig)
from controller.stats.token_counts import TokenCounts, LLMBusiness


class AnalyticsOfflineTask:
    async def analyze(self, dashboard_id: int):
        logger.info(f"Analyze Dashboard[{dashboard_id}]: 任务开始")
        task_start_time = datetime.datetime.now().replace(second=0, microsecond=0)

        # 获取dashboard信息
        dashboard = await Dashboard.get_one(dashboard_id=dashboard_id)
        dashboard_items = await Dashboard.get_item_all(dashboard_id=dashboard_id)
        # 创建分析任务rdb对象
        analyze_task_id = await Metrics.create_task(dashboard_id=dashboard_id, data_time=task_start_time)
        await g.session.commit()
        logger.info(f"Analyze Dashboard[{dashboard_id}]: 分配任务ID{[analyze_task_id]}")

        # 构建任务指标记录
        indicator_list = []
        metrics_id_name_mapping = {}
        for m in dashboard["metrics"]:
            indicator_type = self.metric_type2indicator(m["type_"])
            quantify_indicator = QuantifyIndicatorModel(
                indicator_name=m["name"],
                indicator_description=m["require_"],
                indicator_type=indicator_type,
                number_config=NumberConfig(min_value=m["min_"], max_value=m["max_"]),
                string_config=StringConfig(min_length=m["min_"], max_length=m["max_"])
            )
            indicator_list.append(quantify_indicator)
            metrics_id_name_mapping[m["name"]] = m["metric_id"]

        runners = []
        for item in dashboard_items:
            summarizer_ids = item["combin_summarizer_ids"] + item["sup_summarizer_ids"]
            summarizers = await Summarizer.get_all(summarizer_ids=summarizer_ids)
            tags = list(set(chain.from_iterable([s["tags"] for s in summarizers])))

            # 构建摘要结果对象并记录数据项引用的摘要任务ID
            summarizer_task_ids = []
            abstract_result_list = []
            for sid in summarizer_ids:
                summarizer_task_result = await SummarizerTask.get_task_result(summarizer_id=sid)
                if not summarizer_task_result:
                    continue
                summarizer_task_ids.append(summarizer_task_result["_id"])
                abstract_result_list.append(
                    AbstractResultModel(
                        index=summarizer_task_result["_id"],
                        abstract_content=summarizer_task_result["markdown"]))

            last_item_task_result = await Metrics.get_last_item_success_result(item_id=item["item_id"])
            if (last_item_task_result
                    and last_item_task_result["data_time"] > dashboard["update_time"].strftime("%Y-%m-%d %H:%M:%S")
                    and set(last_item_task_result["summarizer_task_ids"]) == set(summarizer_task_ids)):
                await self.copy_result(
                    last_result=last_item_task_result, task_id=analyze_task_id, data_time=task_start_time)
                logger.info(f"Analyze Dashboard[{dashboard_id}]: 任务未有有效更新, 复制结果至任务 [{last_item_task_result['task_id']}]")
                continue

            # 构建大模型任务runner
            runner = AnalyzeWorkflow(
                analyze_name=dashboard["name"],
                tags=tags,
                user_prompt=dashboard["focus"],
                extra_prompt=dashboard["add_info"],
                abstract_result_list=abstract_result_list,
                indicator_list=indicator_list,
                extra={
                    "item_id": item["item_id"],
                    "summarizer_task_ids": summarizer_task_ids
                }
            )
            runners.append(runner)

        batch = 100
        error_count = 0
        try:
            for i in range(0, len(runners), batch):
                batch_runners = runners[i:i+batch]
                results = await asyncio.gather(*[runner.run() for runner in batch_runners])
                for result in results:
                    metrics_result, source_content = result
                    runner = batch_runners[i]
                    if runner.status.value == 2:
                        logger.info(f"Dashboard_id[{dashboard_id}]: 获取结果成功")
                        extract_metrics = [MetricResult(
                            metric_id=metrics_id_name_mapping[r["indicator_name"]],
                            value=r["value"],
                            explanation=r.get("explanation"),
                        ) for r in metrics_result]

                        item_status = AnalyticsTaskStatus.succeeded.value
                    else:
                        logger.error(f"Analyze Dashboard[{dashboard_id}]: 获取结果失败, {runner.error_message}")
                        error_count += 1
                        extract_metrics = []
                        item_status = AnalyticsTaskStatus.failed.value

                    if runner.token_consumption:
                        input_tokens = sum(runner.token_consumption.input_token)
                        output_tokens = sum(runner.token_consumption.output_token)
                    else:
                        input_tokens = 0
                        output_tokens = 0
                    await TokenCounts.create(model_name="", business=LLMBusiness.analytics, input_tokens=input_tokens,
                                       output_tokens=output_tokens, create_user_id=dashboard["create_user_id"])

                    await Metrics.create_metric(
                        dashboard_id=dashboard_id, task_id=analyze_task_id, item_id=runner.extra["item_id"],
                        summarizer_task_ids=runner.extra["summarizer_task_ids"], extract_metrics=extract_metrics,
                        source_content=source_content, data_time=task_start_time, status=item_status, input_tokens=input_tokens,
                        output_tokens=output_tokens)
                    logger.info(f"Analyze Dashboard[{dashboard_id}]: 数据项 [{runner.extra['item_id']}] 已写入")

        except Exception as err:
            logger.exception(err)
            if len(runners) == len(dashboard_items):
                await Metrics.update_task(dashboard_id=dashboard_id, status=AnalyticsTaskStatus.failed)
            else:
                await Metrics.update_task(dashboard_id=dashboard_id, status=AnalyticsTaskStatus.partially_succeeded)

        else:
            if error_count == 0:
                await Metrics.update_task(dashboard_id=dashboard_id, status=AnalyticsTaskStatus.succeeded)
            else:
                await Metrics.update_task(dashboard_id=dashboard_id, status=AnalyticsTaskStatus.partially_succeeded)

        await g.session.commit()

        logger.info(f"Analyze Dashboard[{dashboard_id}]: 任务完成")


    @staticmethod
    def metric_type2indicator(metric_type: MetricType) -> IndicatorType:
        return {
            MetricType.int: IndicatorType.NUMBER,
            MetricType.str: IndicatorType.STRING
        }[MetricType(metric_type)]

    @staticmethod
    async def copy_result(last_result: dict, task_id: int, data_time: datetime.datetime):
        copy_result = copy.deepcopy(last_result)
        copy_result.update({
            "task_id": task_id,
            "data_time": data_time,
            "input_tokens": 0,
            "output_tokens": 0,
            "copy_to": last_result["task_id"]
        })

        await Metrics.create_metric(**copy_result)


AnalyticsOffline = AnalyticsOfflineTask()


# if __name__ == '__main__':
#     import asyncio
#     from engine.rdb import g, sessionmanager
#
#     async def main():
#         async with sessionmanager.session() as session:
#             g.session = session
#         await AnalyticsOffline.analyze(dashboard_id=12)
#
#     asyncio.run(main())
