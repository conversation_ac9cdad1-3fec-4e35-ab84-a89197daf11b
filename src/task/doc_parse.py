#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from controller.parser.docling_parser import DoclingParser
from controller.parser.chunker import HtmlChunker, BGE_M3_TOKENIZER
from controller.config import Strategy


class DocParseOfflineTask:
    @staticmethod
    async def parsing(doc_id: int) -> bool:
        parser_strategy = await Strategy.get_parser_strategy()

        parser = DoclingParser(
            doc_id=doc_id,
            chunker=HtmlChunker(max_tokens=1024, tokenizer=BGE_M3_TOKENIZER),
            **parser_strategy
        )
        await parser.exec()

        # 成功失败状态
        return True if parser.error is None else False


DocParseOffline = DocParseOfflineTask()
