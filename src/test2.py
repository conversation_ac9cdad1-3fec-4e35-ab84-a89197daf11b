#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from urllib.parse import urlparse
from typing import Dict, Optional


def parse_es_url(es_url: str) -> Dict[str, Optional[str]]:
    """
    解析 Elasticsearch URL，提取 host、port、user、password 等信息

    Args:
        es_url: Elasticsearch URL，格式如 "*************************:port" 或 "https://host:port"

    Returns:
        包含解析结果的字典，包含以下键：
        - scheme: 协议 (http/https)
        - host: 主机地址
        - port: 端口号
        - username: 用户名 (如果有)
        - password: 密码 (如果有)
        - url_without_auth: 不包含认证信息的URL

    Examples:
        >>> parse_es_url("******************************@**************:29200")
        {
            'scheme': 'http',
            'host': '**************',
            'port': '29200',
            'username': 'elastic',
            'password': 'financeTeam@123',
            'url_without_auth': 'http://**************:29200'
        }

        >>> parse_es_url("https://localhost:9200")
        {
            'scheme': 'https',
            'host': 'localhost',
            'port': '9200',
            'username': None,
            'password': None,
            'url_without_auth': 'https://localhost:9200'
        }
    """
    if not es_url:
        raise ValueError("es_url 不能为空")

    try:
        parsed = urlparse(es_url)

        # 提取基本信息
        scheme = parsed.scheme
        host = parsed.hostname
        port = str(parsed.port) if parsed.port else None
        username = parsed.username
        password = parsed.password

        # 构建不包含认证信息的URL
        if host and port:
            url_without_auth = f"{scheme}://{host}:{port}"
        elif host:
            url_without_auth = f"{scheme}://{host}"
        else:
            url_without_auth = es_url

        return {
            'scheme': scheme,
            'host': host,
            'port': port,
            'username': username,
            'password': password,
            'url_without_auth': url_without_auth
        }

    except Exception as e:
        raise ValueError(f"解析 ES URL 失败: {str(e)}")


def get_es_connection_params(es_url: str) -> Dict[str, str]:
    """
    从 ES URL 中提取连接参数，适用于 Elasticsearch 客户端

    Args:
        es_url: Elasticsearch URL

    Returns:
        包含连接参数的字典
    """
    parsed_info = parse_es_url(es_url)

    params = {}

    # 基础连接信息
    if parsed_info['host']:
        params['host'] = parsed_info['host']
    if parsed_info['port']:
        params['port'] = int(parsed_info['port'])
    if parsed_info['scheme']:
        params['scheme'] = parsed_info['scheme']

    # 认证信息
    if parsed_info['username']:
        params['http_auth'] = (parsed_info['username'], parsed_info['password'] or '')

    return params


# 测试示例
if __name__ == "__main__":
    # 测试用例
    test_urls = [
        "******************************@**************:29200",
        "https://localhost:9200",
        "http://user:<EMAIL>:9200",
        "https://example.com",
        "http://*************:9200"
    ]

    for url in test_urls:
        print(f"\n解析 URL: {url}")
        try:
            result = parse_es_url(url)
            print("解析结果:")
            for key, value in result.items():
                print(f"  {key}: {value}")

            print("\n连接参数:")
            params = get_es_connection_params(url)
            for key, value in params.items():
                print(f"  {key}: {value}")

        except Exception as e:
            print(f"解析失败: {e}")
        print("-" * 50)
