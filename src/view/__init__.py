from fastapi import FastAP<PERSON>

from .tool import *
from .api.chat.chat import Chat<PERSON><PERSON><PERSON>, ChatSessionView
from .api.chat.reference import QAReferenceView
from .api.chat.qa_doc import QAChatView, QAChatSessionView
from .api.image.session import ImageSessionView
from .api.chat.memory import ChatMemoryView

from .om.healthcheck import HealthCheckView

from .api.repository.repository import RepositoryView, RepositoryListView
from .api.repository.doc import DocListView, DocView, DocUploadView, DocParsingRetryView
from .api.repository.stats import RepoStatsView
from .api.analytics.summarizer import SummarizerView, SummarizerListView, SummarizerTagsView, SummarizerBulkView
from .api.analytics.summarizer_task import SummarizerTaskListView, SummarizerTaskView

from .api.admin.user import User<PERSON><PERSON>Vie<PERSON>, UserView
from .api.admin.login import <PERSON>gin<PERSON>iew, LogoutView
from .api.admin.self import UserSelfView, ResetPasswordView
from .api.admin.role import RoleAllView

from .api.analytics.dashboard import DashboardListView, DashboardView, DashboardMetricsPartView
from .api.analytics.overview import DashboardOverviewView
from .api.analytics.metrics import DashboardMetricsView
from .api.analytics.charts import DashboardChartView
from .api.analytics.email_delivery import DashboardEmailDeliveryView
from .api.analytics.task import AnalyticsTaskView
from .api.analytics.source import AnalyticsSourceView

from .api.stats.summary import StatsSummaryView
from .api.stats.stats import StatsView

from .api.config.strategy import StrategyView
from .api.config.model import ModelConfigView


# [健康检查]
HealthCheckView(path="/om/healthcheck", tags=["运维"])

# [自由问答]
ChatView(path="/api/chat/completions", tags=["自由问答"])
ChatSessionView(path="/api/chat/session", tags=["自由问答"])
ChatMemoryView(path="/api/chat/memory", tags=["用户记忆"])

# [单文档问答]
QAChatSessionView(path="/api/qa_doc/session", tags=["单文档问答"])
QAChatView(path="/api/qa_doc/completions", tags=["单文档问答"])
QAReferenceView(path="/api/qa_doc/reference", tags=["单文档问答"])

# [文生图]
ImageSessionView(path="/api/image/session", tags=["文生图"])

StatsSummaryView(path="/api/stats/summary", tags=["首页", "统计"])
StatsView(path="/api/stats", tags=["统计"])

RepositoryView(path="/api/repo", tags=["知识库"])
RepositoryListView(path="/api/repo/list", tags=["知识库"])
RepoStatsView(path="/api/repo/stats", tags=["知识库", "统计"])
DocListView(path="/api/repo/doc/list", tags=["知识库", "文档管理"])
DocView(path="/api/repo/doc", tags=["知识库", "文档管理"])
DocUploadView(path="/api/repo/upload", tags=["知识库", "文档管理"])
DocParsingRetryView(path="/api/repo/doc/retry", tags=["知识库", "文档管理"])

SummarizerView(path="/api/analytics/summarizer", tags=["分析", "摘要分析"])
SummarizerListView(path="/api/analytics/summarizer/list", tags=["分析", "摘要分析"])
SummarizerTagsView(path="/api/analytics/summarizer/tags", tags=["分析", "摘要分析"])
SummarizerBulkView(path="/api/analytics/summarizer/bulk", tags=["分析", "摘要分析"])
SummarizerTaskListView(path="/api/analytics/summarizer_task/list", tags=["分析", "摘要分析"])
SummarizerTaskView(path="/api/analytics/summarizer_task", tags=["分析", "摘要分析"])
DashboardListView(path="/api/analytics/dashboard/list", tags=["分析", "分析面板"])
DashboardView(path="/api/analytics/dashboard", tags=["分析", "分析面板"])
DashboardMetricsPartView("/api/analytics/dashboard/part/metrics", tags=["分析", "分析面板"])
DashboardOverviewView(path="/api/analytics/dashboard/overview", tags=["分析", "看板"])
DashboardMetricsView(path="/api/analytics/dashboard/metrics", tags=["分析", "看板"])
DashboardChartView(path="/api/analytics/dashboard/charts", tags=["分析", "看板", "画布"])
DashboardEmailDeliveryView(path="/api/analytics/dashboard/email_delivery", tags=["分析", "看板", "画布"])
AnalyticsTaskView(path="/api/analytics/dashboard/task", tags=["分析", "看板"])
AnalyticsSourceView(path="/api/analytics/source", tags=["分析", "看板"])

UserListView(path="/api/admin/user/list", tags=["权限管理", "用户管理"])
UserView(path="/api/admin/user", tags=["权限管理", "用户管理"])
LoginView(path="/api/login", tags=["权限管理", "登录/登出"])
LogoutView(path="/api/logout", tags=["权限管理", "登录/登出"])
UserSelfView(path="/api/user/self", tags=["权限管理", "用户操作"])
ResetPasswordView(path="/api/user/self/reset_password", tags=["权限管理", "用户操作"])
RoleAllView(path="/api/admin/role/all", tags=["权限管理", "角色管理"])

StrategyView(path="/api/conf/strategy", tags=["配置", "策略配置"])
ModelConfigView(path="/api/conf/model", tags=["配置", "模型配置"])


def register_routes(app: FastAPI):
    app.include_router(router=base_router)


