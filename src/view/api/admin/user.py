#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from typing import Annotated

from fastapi import Query, Body

from view import BaseView, api_description
from controller.admin import User, Auth, AdminRole, NormalRole
from exception.custom_exception import ParamsCheckError, PermissionDenyError
from common.time import strptime
from common.sm import gm_sm2, gm_sm4
from engine.rdb import g


class UserView(BaseView):
    @api_description(summary="创建用户")
    async def post(self,
                   username: Annotated[str, Body(title="用户名称", min_length=6, max_length=24)],
                   nickname: Annotated[str, Body(title="用户昵称", min_length=2, max_length=12)],
                   password: Annotated[str, Body(title="帐号密码")],
                   role_ids: Annotated[list, Body(title="角色ID", min_length=1)],
                   email: Annotated[str, Body(title="用户邮箱", regex=r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$")] = None,
                   phone_number: Annotated[str, Body(title="手机号", regex=r"^1[3-9]\d{9}$")] = None,
                   expire_time: Annotated[str, Body(title="过期时间")] = None,
                   info: Annotated[dict, Body(title="其他信息")] = None):
        if AdminRole.role_id not in self.role_ids:
            raise PermissionDenyError()
        for r_id in role_ids:
            if r_id not in (AdminRole.role_id, NormalRole.role_id):
                raise ParamsCheckError(message=f"不存在的角色ID")
        try:
            password_plaintext = gm_sm2.decrypt(password)
            password = gm_sm4.encrypt(password_plaintext)
        except Exception:
            raise ParamsCheckError(f"密码不合法")
        if expire_time is not None:
            try:
                expire_time = strptime(expire_time, "%Y-%m-%d %H:%M:%S")
            except Exception:
                raise ParamsCheckError("过期时间不合法")

        user = await User.create(
            username=username, password=password, nickname=nickname, role_ids=role_ids, email=email,
            phone_number=phone_number, expire_time=expire_time, info=info)
        await g.session.commit()
        await g.session.refresh(user)

        return self.response(data={"user_id": user.id})

    @api_description(summary="编辑用户")
    async def put(self,
                  user_id: Annotated[int, Body(title="用户ID")],
                  username: Annotated[str, Body(title="用户名称")] = None,
                  password: Annotated[str, Body(title="帐号密码")] = None,
                  nickname: Annotated[str, Body(title="用户昵称")] = None,
                  role_ids: Annotated[list, Body(title="角色ID", min_length=1)] = None,
                  email: Annotated[str, Body(title="用户邮箱", regex=r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$")] = None,
                  phone_number: Annotated[str, Body(title="手机号", regex=r"^1[3-9]\d{9}$")] = None,
                  expire_time: Annotated[str, Body(title="过期时间")] = None,
                  info: Annotated[dict, Body(title="其他信息")] = None):
        if AdminRole.role_id not in self.role_ids:
            raise PermissionDenyError()
        if user_id == 1:
            raise ParamsCheckError("禁止修改初始用户")
        if password:
            try:
                password_plaintext = gm_sm2.decrypt(password)
                password = gm_sm4.encrypt(password_plaintext)
            except Exception:
                raise ParamsCheckError(f"密码不合法")
        if role_ids is not None:
            for r_id in role_ids:
                if r_id not in (AdminRole.role_id, NormalRole.role_id):
                    raise ParamsCheckError(message=f"不存在的角色ID")
        if expire_time is not None:
            try:
                expire_time = strptime(expire_time, "%Y-%m-%d %H:%M:%S")
            except Exception:
                raise ParamsCheckError("过期时间不合法")

        await User.update(
            user_id=user_id, username=username, password=password, nickname=nickname, role_ids=role_ids,
            email=email, phone_number=phone_number, expire_time=expire_time, info=info)
        await g.session.commit()

        Auth.delete_token_cache(user_ids=[user_id])

        return self.response(message="修改成功")

    @api_description(summary="删除用户")
    async def delete(self,
                     user_ids: Annotated[list[int], Body(embed=True, title="用户IDs")]):
        if AdminRole.role_id not in self.role_ids:
            raise PermissionDenyError()
        if 1 in user_ids:
            raise PermissionDenyError(message="禁止删除初始用户")

        await User.delete(user_ids=user_ids)
        await g.session.commit()

        return self.response(message="删除成功")


class UserListView(BaseView):
    @api_description(summary="获取用户列表")
    async def get(self,
                  match: Annotated[str, Query(title="匹配字符")] = None,
                  page: Annotated[int, Query(title="页码")] = 1,
                  per_page: Annotated[int, Query(title="每页数量")] = 20,
                  order_by: Annotated[str, Query(title="排序字段")] = "create_time:desc"):
        if AdminRole.role_id not in self.role_ids:
            raise PermissionDenyError()
        pager, users = await User.get_list(match=match, page=page, per_page=per_page, order_by=order_by)

        return self.response(data=users, pager=pager)
