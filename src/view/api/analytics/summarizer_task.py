#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from typing import Annotated
import itertools

from fastapi import Query
from pydantic import Field

from view import BaseView, api_description
from exception.custom_exception import NotFoundError
from controller.repository import Repo
from controller.analytics import SummarizerTask
from controller.config import ModelInfo, ModelType


class SummarizerTaskListView(BaseView):
    @api_description(summary="摘要分析任务列表")
    async def get(self,
                  repo_id: Annotated[int, Query(), Field(title="知识库ID")] = None,
                  summarizer_id: Annotated[int, Query(), Field(title="")] = None,
                  match: Annotated[str, Query(), Field(title="模糊匹配")] = None,
                  page: Annotated[int, Query(), Field(title="分页页数")] = 1,
                  per_page: Annotated[int, Query(), Field(title="分页容量")] = 20,
                  order_by: Annotated[str, Query(), Field(title="排序字段")] = "create_time:desc"):
        pager, summarizer_tasks = await SummarizerTask.get_list(
            repo_id=repo_id, summarizer_id=summarizer_id, match=match, page=page, per_page=per_page,
            order_by=order_by)
        repo_ids = list(dict.fromkeys(itertools.chain.from_iterable([r["repo_ids"] for r in summarizer_tasks])))
        repo_id_name_mapping = await Repo.get_id_name_mapping(repo_ids)

        task_tokens_mapping = await SummarizerTask.get_task_tokens_mapping(
            summarizer_task_ids=[st["summarizer_task_id"] for st in summarizer_tasks])

        llm_model_id_mapping = await ModelInfo.get_id_mapping(model_type=ModelType.llm)

        for r in summarizer_tasks:
            smz_repo_ids = r.pop("repo_ids")
            r["repo"] = [{
                "repo_id": repo_id,
                "repo_name": repo_id_name_mapping[repo_id]}
                for repo_id in smz_repo_ids]
            if r["summarizer_task_id"] in task_tokens_mapping:
                r.update(task_tokens_mapping[r["summarizer_task_id"]])
            else:
                r["input_tokens"] = r["output_tokens"] = 0

            if r["summarizer_config"].get("llm_models"):
                r["summarizer_config"]["llm_models"] = [llm_model_id_mapping[llm_id] for llm_id in r["summarizer_config"]["llm_models"]]

        return self.response(data=summarizer_tasks, pager=pager)


class SummarizerTaskView(BaseView):
    @api_description(summary="摘要分析任务详情")
    async def get(self,
                  summarizer_task_id: Annotated[int, Query(), Field(title="摘要分析ID")]):
        summarizer_task = await SummarizerTask.get_one(summarizer_task_id=summarizer_task_id)
        if not summarizer_task:
            raise NotFoundError(message="未找到目标结果")

        source = await SummarizerTask.get_source(summarizer_task_id=summarizer_task_id)
        summarizer_task.update(source)

        return self.response(data=summarizer_task)
