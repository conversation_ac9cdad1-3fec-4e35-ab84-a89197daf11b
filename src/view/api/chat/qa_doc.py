from typing import Annotated, List

from fastapi import Query, Body

from view import BaseView, api_description
from controller.chat.doc_qa import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from controller.chat.session import Session, ChatSessionType
from controller.retriever import HybridRetrieval
from controller.config import Strategy, ModelInfo


class QAChatSessionView(BaseView):
    @api_description(summary="查询会话")
    async def get(self,
                  session_id: Annotated[str, Query(title="Session ID")]):
        session_item = await Session.get_one(session_id=session_id)
        return self.response(data=session_item.model_dump())

    @api_description(summary="创建会话")
    async def post(self):
        session_item = await Session.create(ChatSessionType.QA_DOC)
        return self.response(data=session_item.model_dump())


class QAChatView(BaseView):
    @api_description(summary="开始自由问答")
    async def post(self,
                   user: Annotated[str, Body(title="用户输入的问题")],
                   session_id: Annotated[str, Body(title="会话ID")] = None,
                   repo_ids: Annotated[list[int], Body(title="知识库IDs")] = None,
                   doc_ids: Annotated[List[int], Body(title="文档ID")] = None):
        if session_id is None:
            session_item = await Session.create(ChatSessionType.QA_DOC)
            session_id = session_item.session_id

        chat_retrieval_strategy = await Strategy.get_chat_retrieval_strategy()

        retrieval = HybridRetrieval(
            repo_ids=repo_ids,
            doc_ids=doc_ids,
            max_doc_size=len(doc_ids) if doc_ids else chat_retrieval_strategy.pop("max_doc_size"),
            **chat_retrieval_strategy)
        # @chen: 这里支持history参数
        retrieve_chunks = await retrieval.retrieve(query=user)

        chat_helper = DocQAHelper(session_id=session_id, chunks=retrieve_chunks, user=user)
        return self.stream(chat_helper.generator())
