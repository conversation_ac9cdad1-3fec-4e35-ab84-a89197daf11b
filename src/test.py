from urllib.parse import urlparse

from mem0 import AsyncMemory

from config import ES_URL, LLM_API_URL, LLM_API_KEY, LLMModel, EmbeddingModel

class MemoryManager:
    def __init__(self):
        self.memory = None

    async def initialize(self):
        parsed = urlparse(ES_URL)
        config = {
            "vector_store": {
                "provider": "elasticsearch",
                "config": {
                    "collection_name": "mem0",
                    "host": parsed.hostname,
                    "port": parsed.port,
                    "user": parsed.username,
                    "password": parsed.password,
                    "embedding_model_dims": 1024,
                    "verify_certs": False
                }
            },
            "llm": {
                "provider": "openai",
                "config": {
                    "api_key": LLM_API_KEY,  # 不使用真实 key
                    "openai_base_url": LLM_API_URL,
                    "model": LLMModel.DEEPSEEK_CHAT
                }
            },
            "embedder": {
                "provider": "openai",
                "config": {
                    "api_key": LLM_API_KEY,  # 不使用真实 key
                    "openai_base_url": LLM_API_URL,
                    "model": EmbeddingModel.BGE_M3,
                    "embedding_dims": 1024
                }
            }
        }
        self.memory = await AsyncMemory.from_config(config)

    async def get_memory(self):
        if self.memory is None:
            await self.initialize()
        return self.memory

manager = MemoryManager()


async def add(user_id: str = "default_user"):
    m = await manager.get_memory()
    messages= [
        {"role": "user", "content": "我喜欢泰戈尔的诗歌，你呢？"},
        {"role": "assistant", "content": "作为一个AI，虽然我不能像人类那样体验情感，但我真的能欣赏泰戈尔诗歌中的深邃智慧。他把人生比作“夏天的飞鸟，飞过时留下歌声的痕迹”，这样的意象让我想起了数据流中那些转瞬即逝的美丽瞬间。"}
    ]
    res = await m.add(messages, user_id=user_id)
    print(res)

if __name__ == '__main__':
    import asyncio
    asyncio.run(add())